# PaddleOCR Integration for NowReceipt

This document describes the PaddleOCR integration implemented in the NowReceipt Android application.

## Overview

The application now supports two OCR engines:
1. **Google ML Kit** (default fallback)
2. **PaddleOCR** (primary engine)

## Implementation Details

### Files Added/Modified

1. **PaddleOCREngine.kt** - Main PaddleOCR integration class
2. **PaddleOCRLocal.kt** - Local model inference implementation
3. **ReceiptScanner.kt** - Updated to support multiple OCR engines
4. **MainActivity.kt** - Updated to initialize OCR engine
5. **AndroidManifest.xml** - Added internet permissions
6. **build.gradle.kts** - Added networking dependencies

### Architecture

The implementation supports two approaches:

#### 1. REST API Approach (Current Default)
- Uses HTTP API calls to a PaddleOCR server
- Requires internet connection
- Easy to deploy and maintain
- No local model files needed

#### 2. Local Model Approach (Future Enhancement)
- Uses TensorFlow Lite models converted from PaddleOCR
- Works offline
- Requires model files in app assets
- Better privacy and performance

## Configuration

### OCR Engine Selection

In `ReceiptScanner.kt`, you can choose the OCR engine:

```kotlin
class ReceiptScanner(
    private val context: android.content.Context,
    private val ocrEngine: OCREngine = OCREngine.PADDLE_OCR // or OCREngine.ML_KIT
)
```

### PaddleOCR Configuration

Configure PaddleOCR settings in `ReceiptScanner.kt`:

```kotlin
private val paddleOCREngine = PaddleOCREngine(
    context = context,
    config = PaddleOCRConfig(
        useLocalModel = false, // Set to true for local models
        apiEndpoint = "https://your-paddleocr-api.com/", // Your API endpoint
        enableDetection = true,
        enableRecognition = true,
        enableClassification = false,
        imageQuality = 85
    )
)
```

## Setup Instructions

### Option 1: Using PaddleOCR API Server

1. **Deploy PaddleOCR Server**
   ```bash
   # Install PaddleOCR
   pip install paddleocr
   
   # Create a simple Flask server
   # See example server code below
   ```

2. **Update API Endpoint**
   - Replace `"https://your-paddleocr-api.com/"` with your actual API endpoint
   - Ensure the server accepts POST requests with base64 encoded images

3. **Test the Integration**
   - Build and run the app
   - Try scanning a receipt
   - Check logs for initialization and API call status

### Option 2: Using Local Models (Advanced)

1. **Convert PaddleOCR Models to TensorFlow Lite**
   ```bash
   # This requires advanced model conversion
   # Follow PaddleOCR documentation for model export
   ```

2. **Add Model Files to Assets**
   - Place `.tflite` model files in `app/src/main/assets/`
   - Required files:
     - `paddle_det_model.tflite` (text detection)
     - `paddle_rec_model.tflite` (text recognition)
     - `paddle_cls_model.tflite` (text classification, optional)

3. **Enable Local Models**
   ```kotlin
   config = PaddleOCRConfig(
       useLocalModel = true,
       // ... other settings
   )
   ```

## Example PaddleOCR API Server

Here's a simple Flask server example:

```python
from flask import Flask, request, jsonify
from paddleocr import PaddleOCR
import base64
import cv2
import numpy as np

app = Flask(__name__)
ocr = PaddleOCR(use_angle_cls=True, lang='en')

@app.route('/predict/ocr_system', methods=['POST'])
def ocr_predict():
    try:
        data = request.json
        image_data = base64.b64decode(data['image'])
        
        # Convert to OpenCV image
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Run OCR
        result = ocr.ocr(img, det=data.get('det', True), 
                        rec=data.get('rec', True), 
                        cls=data.get('cls', False))
        
        # Format response
        ocr_results = []
        if result and result[0]:
            for line in result[0]:
                bbox = line[0]
                text = line[1][0]
                confidence = line[1][1]
                
                # Convert bbox to [x1, y1, x2, y2] format
                x1, y1 = int(min(bbox, key=lambda x: x[0])[0]), int(min(bbox, key=lambda x: x[1])[1])
                x2, y2 = int(max(bbox, key=lambda x: x[0])[0]), int(max(bbox, key=lambda x: x[1])[1])
                
                ocr_results.append({
                    "text": text,
                    "confidence": confidence,
                    "bbox": [x1, y1, x2, y2]
                })
        
        return jsonify({
            "status": "success",
            "results": ocr_results,
            "message": "OCR completed successfully"
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "results": [],
            "message": str(e)
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8866)
```

## Fallback Mechanism

The implementation includes automatic fallback:
1. If PaddleOCR fails, the app automatically falls back to Google ML Kit
2. This ensures the app continues to work even if the PaddleOCR service is unavailable
3. Error messages are logged for debugging

## Dependencies Added

```kotlin
// Networking for PaddleOCR API integration
implementation("com.squareup.retrofit2:retrofit:2.9.0")
implementation("com.squareup.retrofit2:converter-gson:2.9.0")
implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
```

## Permissions Added

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## Testing

1. **Test with ML Kit**: Set `ocrEngine = OCREngine.ML_KIT` to verify existing functionality
2. **Test with PaddleOCR**: Set `ocrEngine = OCREngine.PADDLE_OCR` and ensure API endpoint is configured
3. **Test Fallback**: Temporarily break the API endpoint to verify fallback to ML Kit works

## Performance Considerations

- **API Approach**: Depends on network speed and server performance
- **Local Approach**: Faster inference but larger app size due to model files
- **Image Quality**: Adjust `imageQuality` setting to balance between accuracy and upload speed

## Troubleshooting

1. **API Connection Issues**
   - Check internet connectivity
   - Verify API endpoint URL
   - Check server logs for errors

2. **Local Model Issues**
   - Ensure model files are in the correct assets directory
   - Check model file sizes and formats
   - Verify TensorFlow Lite compatibility

3. **Initialization Failures**
   - Check app logs for initialization errors
   - Verify permissions are granted
   - Ensure proper context is passed to ReceiptScanner

## Future Enhancements

1. **Model Optimization**: Implement proper post-processing for local models
2. **Caching**: Add response caching for API calls
3. **Batch Processing**: Support multiple image processing
4. **Language Support**: Add multi-language OCR support
5. **Confidence Thresholding**: Implement confidence-based result filtering
