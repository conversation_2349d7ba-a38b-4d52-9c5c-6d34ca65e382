package com.arrayberry.nowreceipt

import android.graphics.Bitmap
import android.util.Base64
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST
import java.io.ByteArrayOutputStream

/**
 * PaddleOCR Engine for text extraction from images
 * This implementation supports both REST API and local model approaches
 */
class PaddleOCREngine(
    private val context: android.content.Context,
    private val config: PaddleOCRConfig = PaddleOCRConfig()
) {

    companion object {
        private const val TAG = "PaddleOCREngine"
    }

    private val retrofit = Retrofit.Builder()
        .baseUrl(config.apiEndpoint)
        .addConverterFactory(GsonConverterFactory.create())
        .build()

    private val apiService = retrofit.create(PaddleOCRApiService::class.java)
    private val localEngine = PaddleOCRLocal(context)

    private var isLocalEngineInitialized = false
    
    /**
     * Initialize the PaddleOCR engine
     */
    suspend fun initialize(): Boolean {
        return if (config.useLocalModel) {
            isLocalEngineInitialized = localEngine.initialize()
            isLocalEngineInitialized
        } else {
            true // API doesn't need initialization
        }
    }

    /**
     * Extract text from bitmap using PaddleOCR
     */
    suspend fun extractText(bitmap: Bitmap): OCRResult {
        return if (config.useLocalModel) {
            extractTextLocal(bitmap)
        } else {
            extractTextAPI(bitmap)
        }
    }

    /**
     * Extract text using API
     */
    private suspend fun extractTextAPI(bitmap: Bitmap): OCRResult {
        return withContext(Dispatchers.IO) {
            try {
                val base64Image = bitmapToBase64(bitmap)
                val request = OCRRequest(
                    image = base64Image,
                    det = config.enableDetection,
                    rec = config.enableRecognition,
                    cls = config.enableClassification
                )

                val response = apiService.recognizeText(request)

                if (response.isSuccessful && response.body() != null) {
                    response.body()!!
                } else {
                    Log.e(TAG, "API call failed: ${response.errorBody()?.string()}")
                    OCRResult(
                        status = "error",
                        results = emptyList(),
                        message = "API call failed: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during API OCR processing", e)
                OCRResult(
                    status = "error",
                    results = emptyList(),
                    message = "API Error: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Extract text using local models
     */
    private suspend fun extractTextLocal(bitmap: Bitmap): OCRResult {
        return if (isLocalEngineInitialized) {
            localEngine.extractText(bitmap)
        } else {
            Log.w(TAG, "Local engine not initialized, falling back to API")
            extractTextAPI(bitmap)
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        localEngine.cleanup()
    }
    
    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, config.imageQuality, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }
}

/**
 * Retrofit API service for PaddleOCR
 */
interface PaddleOCRApiService {
    @POST("predict/ocr_system")
    suspend fun recognizeText(@Body request: OCRRequest): retrofit2.Response<OCRResult>
}

/**
 * Request model for PaddleOCR API
 */
data class OCRRequest(
    val image: String, // Base64 encoded image
    val det: Boolean = true, // Enable text detection
    val rec: Boolean = true, // Enable text recognition
    val cls: Boolean = false // Enable text direction classification
)

/**
 * Response model for PaddleOCR API
 */
data class OCRResult(
    val status: String,
    val results: List<OCRTextResult>,
    val message: String? = null
)

/**
 * Individual text result from OCR
 */
data class OCRTextResult(
    val text: String,
    val confidence: Float,
    val bbox: List<Int> // Bounding box coordinates [x1, y1, x2, y2]
)

/**
 * Configuration for PaddleOCR engine
 */
data class PaddleOCRConfig(
    val useLocalModel: Boolean = false,
    val apiEndpoint: String = "https://your-paddleocr-api.com/",
    val enableDetection: Boolean = true,
    val enableRecognition: Boolean = true,
    val enableClassification: Boolean = false,
    val imageQuality: Int = 80 // JPEG compression quality
)
