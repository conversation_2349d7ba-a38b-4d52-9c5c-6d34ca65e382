package com.arrayberry.nowreceipt

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.Photo
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.arrayberry.nowreceipt.ui.theme.NowReceiptTheme
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : ComponentActivity() {
    
    private lateinit var receiptScanner: ReceiptScanner
    private lateinit var fileManager: FileManager
    private lateinit var database: ReceiptDatabase
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        receiptScanner = ReceiptScanner(this)
        fileManager = FileManager(this)
        database = ReceiptDatabase.getDatabase(this)

        // Initialize the OCR engine
        lifecycleScope.launch {
            try {
                val initialized = receiptScanner.initialize()
                Log.d("MainActivity", "OCR Engine initialized: $initialized")
            } catch (e: Exception) {
                Log.e("MainActivity", "Failed to initialize OCR engine", e)
            }
        }
        enableEdgeToEdge()
        setContent {
            NowReceiptTheme {
                val navController = rememberNavController()
                
                NavHost(
                    navController = navController,
                    startDestination = "scanner"
                ) {
                    composable("scanner") {
                        Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                            ReceiptScannerScreen(
                                receiptScanner = receiptScanner,
                                fileManager = fileManager,
                                database = database,
                                lifecycleScope = lifecycleScope,
                                navController = navController,
                                modifier = Modifier.padding(innerPadding)
                            )
                        }
                    }
                    
                    composable("saved_receipts") {
                        Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                            SavedReceiptsScreen(
                                database = database,
                                fileManager = fileManager,
                                navController = navController,
                                modifier = Modifier.padding(innerPadding)
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        receiptScanner.cleanup()
    }
}

@Composable
fun ReceiptScannerScreen(
    receiptScanner: ReceiptScanner,
    fileManager: FileManager,
    database: ReceiptDatabase,
    lifecycleScope: kotlinx.coroutines.CoroutineScope,
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var receiptData by remember { mutableStateOf<ReceiptData?>(null) }
    var isScanning by remember { mutableStateOf(false) }
    var isSaved by remember { mutableStateOf(false) }
    var currentBitmap by remember { mutableStateOf<Bitmap?>(null) }
    
    // Editable fields
    var editableMerchant by remember { mutableStateOf("") }
    var editableDate by remember { mutableStateOf("") }
    var editableAmount by remember { mutableStateOf("") }
    
    val imageUri = remember {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFile = File(context.cacheDir, "receipt_$timeStamp.jpg")
        FileProvider.getUriForFile(context, "${context.packageName}.provider", imageFile)
    }
    
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            isScanning = true
            try {
                val inputStream = context.contentResolver.openInputStream(imageUri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
                
                currentBitmap = bitmap
                receiptScanner.scanReceipt(bitmap) { result ->
                    receiptData = result
                    editableMerchant = result.merchantName ?: ""
                    editableDate = result.date ?: ""
                    editableAmount = result.amount ?: ""
                    isScanning = false
                    isSaved = false
                }
            } catch (e: Exception) {
                Toast.makeText(context, "Error processing image: ${e.message}", Toast.LENGTH_SHORT).show()
                isScanning = false
            }
        }
    }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            cameraLauncher.launch(imageUri)
        } else {
            Toast.makeText(context, "Camera permission is required", Toast.LENGTH_SHORT).show()
        }
    }

    val photoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            isScanning = true
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()

                currentBitmap = bitmap
                receiptScanner.scanReceipt(bitmap) { result ->
                    receiptData = result
                    editableMerchant = result.merchantName ?: ""
                    editableDate = result.date ?: ""
                    editableAmount = result.amount ?: ""
                    isScanning = false
                    isSaved = false
                }
            } catch (e: Exception) {
                Toast.makeText(context, "Error processing image: ${e.message}", Toast.LENGTH_SHORT).show()
                isScanning = false
            }
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "NowReceipt",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Button(
                onClick = {
                    when (ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA)) {
                        PackageManager.PERMISSION_GRANTED -> {
                            cameraLauncher.launch(imageUri)
                        }
                        else -> {
                            permissionLauncher.launch(Manifest.permission.CAMERA)
                        }
                    }
                },
                enabled = !isScanning,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.CameraAlt,
                    contentDescription = "Camera",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("Scan Receipt")
            }

            Button(
                onClick = {
                    photoPickerLauncher.launch("image/*")
                },
                enabled = !isScanning,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Photo,
                    contentDescription = "Photo Gallery",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("From Photo")
            }

            OutlinedButton(
                onClick = { navController.navigate("saved_receipts") },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("View Saved")
            }
        }
        
        
        if (isScanning) {
            CircularProgressIndicator(modifier = Modifier.padding(16.dp))
            Text("Scanning receipt...")
        }
        
        receiptData?.let { data ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Receipt Details",
                            style = MaterialTheme.typography.headlineSmall
                        )
                        
                        if (isSaved) {
                            Text(
                                text = "✓ Saved",
                                color = MaterialTheme.colorScheme.primary,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Editable Merchant Field
                    OutlinedTextField(
                        value = editableMerchant,
                        onValueChange = { editableMerchant = it },
                        label = { Text("Merchant Name") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                    )
                    
                    // Editable Date Field
                    OutlinedTextField(
                        value = editableDate,
                        onValueChange = { editableDate = it },
                        label = { Text("Date") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                    )
                    
                    // Editable Amount Field
                    OutlinedTextField(
                        value = editableAmount,
                        onValueChange = { editableAmount = it },
                        label = { Text("Amount") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    )
                    
                    // Save Button
                    Button(
                        onClick = {
                            lifecycleScope.launch {
                                try {
                                    val photoPath = currentBitmap?.let { bitmap ->
                                        fileManager.saveReceiptPhoto(bitmap)
                                    }
                                    
                                    val receipt = ReceiptEntity(
                                        merchantName = editableMerchant.takeIf { it.isNotBlank() },
                                        date = editableDate.takeIf { it.isNotBlank() },
                                        amount = editableAmount.takeIf { it.isNotBlank() },
                                        rawText = data.rawText,
                                        photoPath = photoPath
                                    )
                                    
                                    database.receiptDao().insertReceipt(receipt)
                                    isSaved = true
                                    
                                    Toast.makeText(context, "Receipt saved successfully!", Toast.LENGTH_SHORT).show()
                                } catch (e: Exception) {
                                    Toast.makeText(context, "Error saving receipt: ${e.message}", Toast.LENGTH_SHORT).show()
                                }
                            }
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isSaved
                    ) {
                        Text(if (isSaved) "Saved" else "Save Receipt")
                    }
                    
                    if (data.rawText.isNotEmpty()) {
                        Text(
                            text = "Raw Text:",
                            style = MaterialTheme.typography.labelLarge,
                            modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                        )
                        Text(
                            text = data.rawText,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                }
            }
        }
    }
}

