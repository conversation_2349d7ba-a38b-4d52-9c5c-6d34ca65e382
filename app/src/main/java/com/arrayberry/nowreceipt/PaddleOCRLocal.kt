package com.arrayberry.nowreceipt

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.tensorflow.lite.Interpreter
import java.io.FileInputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.MappedByteBuffer
import java.nio.channels.FileChannel

/**
 * Local PaddleOCR implementation using TensorFlow Lite
 * This class provides offline text recognition using converted PaddleOCR models
 */
class PaddleOCRLocal(private val context: Context) {
    
    companion object {
        private const val TAG = "PaddleOCRLocal"
        
        // Model configuration
        private const val DET_MODEL_FILE = "paddle_det_model.tflite"
        private const val REC_MODEL_FILE = "paddle_rec_model.tflite"
        private const val CLS_MODEL_FILE = "paddle_cls_model.tflite"
        
        // Input image dimensions for detection model
        private const val DET_INPUT_WIDTH = 960
        private const val DET_INPUT_HEIGHT = 960
        
        // Input image dimensions for recognition model
        private const val REC_INPUT_WIDTH = 320
        private const val REC_INPUT_HEIGHT = 48
        
        // Model input/output configurations
        private const val PIXEL_SIZE = 3 // RGB
        private const val BYTES_PER_CHANNEL = 4 // Float32
    }
    
    private var detectionInterpreter: Interpreter? = null
    private var recognitionInterpreter: Interpreter? = null
    private var classificationInterpreter: Interpreter? = null
    
    private var isInitialized = false
    
    /**
     * Initialize the PaddleOCR models
     */
    suspend fun initialize(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Load detection model
                val detModelBuffer = loadModelFile(DET_MODEL_FILE)
                if (detModelBuffer != null) {
                    detectionInterpreter = Interpreter(detModelBuffer)
                    Log.d(TAG, "Detection model loaded successfully")
                }
                
                // Load recognition model
                val recModelBuffer = loadModelFile(REC_MODEL_FILE)
                if (recModelBuffer != null) {
                    recognitionInterpreter = Interpreter(recModelBuffer)
                    Log.d(TAG, "Recognition model loaded successfully")
                }
                
                // Load classification model (optional)
                val clsModelBuffer = loadModelFile(CLS_MODEL_FILE)
                if (clsModelBuffer != null) {
                    classificationInterpreter = Interpreter(clsModelBuffer)
                    Log.d(TAG, "Classification model loaded successfully")
                }
                
                isInitialized = detectionInterpreter != null && recognitionInterpreter != null
                Log.i(TAG, "PaddleOCR Local initialization: ${if (isInitialized) "SUCCESS" else "FAILED"}")
                
                isInitialized
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize PaddleOCR Local", e)
                false
            }
        }
    }
    
    /**
     * Extract text from bitmap using local PaddleOCR models
     */
    suspend fun extractText(bitmap: Bitmap): OCRResult {
        return withContext(Dispatchers.Default) {
            if (!isInitialized) {
                return@withContext OCRResult(
                    status = "error",
                    results = emptyList(),
                    message = "PaddleOCR Local not initialized. Models may be missing."
                )
            }
            
            try {
                // Step 1: Text Detection
                val textBoxes = detectText(bitmap)
                if (textBoxes.isEmpty()) {
                    return@withContext OCRResult(
                        status = "success",
                        results = emptyList(),
                        message = "No text detected in image"
                    )
                }
                
                // Step 2: Text Recognition for each detected box
                val results = mutableListOf<OCRTextResult>()
                for (box in textBoxes) {
                    val croppedBitmap = cropTextRegion(bitmap, box)
                    val recognizedText = recognizeText(croppedBitmap)
                    
                    if (recognizedText.isNotBlank()) {
                        results.add(
                            OCRTextResult(
                                text = recognizedText,
                                confidence = 0.8f, // Placeholder confidence
                                bbox = box
                            )
                        )
                    }
                }
                
                OCRResult(
                    status = "success",
                    results = results,
                    message = "Text extraction completed"
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during text extraction", e)
                OCRResult(
                    status = "error",
                    results = emptyList(),
                    message = "Text extraction failed: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Detect text regions in the image
     */
    private fun detectText(bitmap: Bitmap): List<List<Int>> {
        val interpreter = detectionInterpreter ?: return emptyList()
        
        try {
            // Preprocess image for detection
            val inputBuffer = preprocessImageForDetection(bitmap)
            
            // Prepare output buffer
            val outputShape = interpreter.getOutputTensor(0).shape()
            val outputBuffer = ByteBuffer.allocateDirect(outputShape.fold(1) { acc, dim -> acc * dim } * BYTES_PER_CHANNEL)
            outputBuffer.order(ByteOrder.nativeOrder())
            
            // Run inference
            interpreter.run(inputBuffer, outputBuffer)
            
            // Post-process detection results
            return postprocessDetectionResults(outputBuffer, outputShape)
            
        } catch (e: Exception) {
            Log.e(TAG, "Text detection failed", e)
            return emptyList()
        }
    }
    
    /**
     * Recognize text in a cropped image region
     */
    private fun recognizeText(bitmap: Bitmap): String {
        val interpreter = recognitionInterpreter ?: return ""
        
        try {
            // Preprocess image for recognition
            val inputBuffer = preprocessImageForRecognition(bitmap)
            
            // Prepare output buffer
            val outputShape = interpreter.getOutputTensor(0).shape()
            val outputBuffer = ByteBuffer.allocateDirect(outputShape.fold(1) { acc, dim -> acc * dim } * BYTES_PER_CHANNEL)
            outputBuffer.order(ByteOrder.nativeOrder())
            
            // Run inference
            interpreter.run(inputBuffer, outputBuffer)
            
            // Post-process recognition results
            return postprocessRecognitionResults(outputBuffer, outputShape)
            
        } catch (e: Exception) {
            Log.e(TAG, "Text recognition failed", e)
            return ""
        }
    }
    
    /**
     * Load model file from assets
     */
    private fun loadModelFile(modelFileName: String): MappedByteBuffer? {
        return try {
            val fileDescriptor = context.assets.openFd(modelFileName)
            val inputStream = FileInputStream(fileDescriptor.fileDescriptor)
            val fileChannel = inputStream.channel
            val startOffset = fileDescriptor.startOffset
            val declaredLength = fileDescriptor.declaredLength
            fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
        } catch (e: Exception) {
            Log.w(TAG, "Model file $modelFileName not found in assets", e)
            null
        }
    }
    
    /**
     * Preprocess image for detection model
     */
    private fun preprocessImageForDetection(bitmap: Bitmap): ByteBuffer {
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, DET_INPUT_WIDTH, DET_INPUT_HEIGHT, true)
        val inputBuffer = ByteBuffer.allocateDirect(DET_INPUT_WIDTH * DET_INPUT_HEIGHT * PIXEL_SIZE * BYTES_PER_CHANNEL)
        inputBuffer.order(ByteOrder.nativeOrder())
        
        val pixels = IntArray(DET_INPUT_WIDTH * DET_INPUT_HEIGHT)
        resizedBitmap.getPixels(pixels, 0, DET_INPUT_WIDTH, 0, 0, DET_INPUT_WIDTH, DET_INPUT_HEIGHT)
        
        for (pixel in pixels) {
            // Normalize pixel values to [0, 1] and convert to RGB
            inputBuffer.putFloat(((pixel shr 16) and 0xFF) / 255.0f) // R
            inputBuffer.putFloat(((pixel shr 8) and 0xFF) / 255.0f)  // G
            inputBuffer.putFloat((pixel and 0xFF) / 255.0f)          // B
        }
        
        return inputBuffer
    }
    
    /**
     * Preprocess image for recognition model
     */
    private fun preprocessImageForRecognition(bitmap: Bitmap): ByteBuffer {
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, REC_INPUT_WIDTH, REC_INPUT_HEIGHT, true)
        val inputBuffer = ByteBuffer.allocateDirect(REC_INPUT_WIDTH * REC_INPUT_HEIGHT * PIXEL_SIZE * BYTES_PER_CHANNEL)
        inputBuffer.order(ByteOrder.nativeOrder())
        
        val pixels = IntArray(REC_INPUT_WIDTH * REC_INPUT_HEIGHT)
        resizedBitmap.getPixels(pixels, 0, REC_INPUT_WIDTH, 0, 0, REC_INPUT_WIDTH, REC_INPUT_HEIGHT)
        
        for (pixel in pixels) {
            // Normalize pixel values to [0, 1] and convert to RGB
            inputBuffer.putFloat(((pixel shr 16) and 0xFF) / 255.0f) // R
            inputBuffer.putFloat(((pixel shr 8) and 0xFF) / 255.0f)  // G
            inputBuffer.putFloat((pixel and 0xFF) / 255.0f)          // B
        }
        
        return inputBuffer
    }
    
    /**
     * Post-process detection results to extract bounding boxes
     */
    private fun postprocessDetectionResults(outputBuffer: ByteBuffer, outputShape: IntArray): List<List<Int>> {
        // This is a simplified implementation
        // In a real implementation, you would parse the detection model output
        // to extract text bounding boxes with proper confidence thresholding
        
        // Placeholder implementation - returns empty list
        // TODO: Implement proper detection post-processing based on PaddleOCR detection model output format
        return emptyList()
    }
    
    /**
     * Post-process recognition results to extract text
     */
    private fun postprocessRecognitionResults(outputBuffer: ByteBuffer, outputShape: IntArray): String {
        // This is a simplified implementation
        // In a real implementation, you would decode the recognition model output
        // using the character dictionary and CTC decoding
        
        // Placeholder implementation
        // TODO: Implement proper recognition post-processing based on PaddleOCR recognition model output format
        return "Placeholder text"
    }
    
    /**
     * Crop text region from original image based on bounding box
     */
    private fun cropTextRegion(bitmap: Bitmap, bbox: List<Int>): Bitmap {
        val x1 = bbox[0].coerceAtLeast(0)
        val y1 = bbox[1].coerceAtLeast(0)
        val x2 = bbox[2].coerceAtMost(bitmap.width)
        val y2 = bbox[3].coerceAtMost(bitmap.height)
        
        val width = (x2 - x1).coerceAtLeast(1)
        val height = (y2 - y1).coerceAtLeast(1)
        
        return Bitmap.createBitmap(bitmap, x1, y1, width, height)
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        detectionInterpreter?.close()
        recognitionInterpreter?.close()
        classificationInterpreter?.close()
        
        detectionInterpreter = null
        recognitionInterpreter = null
        classificationInterpreter = null
        
        isInitialized = false
        Log.d(TAG, "PaddleOCR Local resources cleaned up")
    }
}
