package com.arrayberry.nowreceipt

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.regex.Pattern

enum class OCREngine {
    ML_KIT,
    PADDLE_OCR
}

class ReceiptScanner(
    private val context: android.content.Context,
    private val ocrEngine: OCREngine = OCREngine.PADDLE_OCR
) {

    companion object {
        private const val TAG = "ReceiptScanner"
    }

    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    private val paddleOCREngine = PaddleOCREngine(
        context = context,
        config = PaddleOCRConfig(
            useLocalModel = false, // Set to true when you have local models
            apiEndpoint = "https://your-paddleocr-api.com/", // Replace with your API endpoint
            enableDetection = true,
            enableRecognition = true,
            enableClassification = false,
            imageQuality = 85
        )
    )
    private val textProcessor = TextProcessor()

    /**
     * Initialize the OCR engine (call this before using scanReceipt)
     */
    suspend fun initialize(): Boolean {
        return when (ocrEngine) {
            OCREngine.ML_KIT -> true // ML Kit doesn't need initialization
            OCREngine.PADDLE_OCR -> paddleOCREngine.initialize()
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        paddleOCREngine.cleanup()
    }

    fun scanReceipt(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        when (ocrEngine) {
            OCREngine.ML_KIT -> scanWithMLKit(bitmap, callback)
            OCREngine.PADDLE_OCR -> scanWithPaddleOCR(bitmap, callback)
        }
    }

    private fun scanWithMLKit(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        val image = InputImage.fromBitmap(bitmap, 0)

        textRecognizer.process(image)
            .addOnSuccessListener { visionText ->
                val rawText = visionText.text
                val receiptData = extractReceiptFieldsWithML(rawText)
                callback(receiptData)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "ML Kit OCR failed", e)
                callback(ReceiptData(rawText = "Error scanning receipt: ${e.message}"))
            }
    }

    private fun scanWithPaddleOCR(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val ocrResult = paddleOCREngine.extractText(bitmap)
                val rawText = convertOCRResultToText(ocrResult)
                val receiptData = extractReceiptFieldsWithML(rawText)
                callback(receiptData)
            } catch (e: Exception) {
                Log.e(TAG, "PaddleOCR failed", e)
                // Fallback to ML Kit if PaddleOCR fails
                Log.i(TAG, "Falling back to ML Kit OCR")
                scanWithMLKit(bitmap, callback)
            }
        }
    }

    private fun convertOCRResultToText(ocrResult: OCRResult): String {
        return if (ocrResult.status == "success") {
            ocrResult.results.joinToString("\n") { it.text }
        } else {
            "PaddleOCR Error: ${ocrResult.message ?: "Unknown error"}"
        }
    }
    
    private fun extractReceiptFieldsWithML(text: String): ReceiptData {
        val lines = textProcessor.preprocessText(text)
        
        val merchantName = extractBestMerchant(lines)
        val date = extractBestDate(lines)
        val amount = extractBestAmount(lines)
        
        return ReceiptData(
            merchantName = merchantName,
            date = date,
            amount = amount,
            rawText = text
        )
    }
    
    private fun extractBestMerchant(lines: List<String>): String? {
        val candidates = textProcessor.extractMerchantCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestDate(lines: List<String>): String? {
        val candidates = textProcessor.extractDateCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestAmount(lines: List<String>): String? {
        val candidates = textProcessor.extractAmountCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
}