package com.arrayberry.nowreceipt

import android.content.Context
import android.graphics.Bitmap
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@RunWith(MockitoJUnitRunner::class)
class PaddleOCREngineTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockBitmap: Bitmap

    private lateinit var paddleOCREngine: PaddleOCREngine

    @Before
    fun setup() {
        val config = PaddleOCRConfig(
            useLocalModel = false,
            apiEndpoint = "https://test-api.com/",
            enableDetection = true,
            enableRecognition = true,
            enableClassification = false,
            imageQuality = 80
        )
        paddleOCREngine = PaddleOCREngine(mockContext, config)
    }

    @Test
    fun `test PaddleOCR configuration`() {
        val config = PaddleOCRConfig(
            useLocalModel = true,
            apiEndpoint = "https://custom-api.com/",
            enableDetection = false,
            enableRecognition = true,
            enableClassification = true,
            imageQuality = 90
        )

        assertEquals(true, config.useLocalModel)
        assertEquals("https://custom-api.com/", config.apiEndpoint)
        assertEquals(false, config.enableDetection)
        assertEquals(true, config.enableRecognition)
        assertEquals(true, config.enableClassification)
        assertEquals(90, config.imageQuality)
    }

    @Test
    fun `test OCR result data classes`() {
        val textResult = OCRTextResult(
            text = "Test Receipt",
            confidence = 0.95f,
            bbox = listOf(10, 20, 100, 40)
        )

        val ocrResult = OCRResult(
            status = "success",
            results = listOf(textResult),
            message = "OCR completed"
        )

        assertEquals("Test Receipt", textResult.text)
        assertEquals(0.95f, textResult.confidence)
        assertEquals(listOf(10, 20, 100, 40), textResult.bbox)

        assertEquals("success", ocrResult.status)
        assertEquals(1, ocrResult.results.size)
        assertEquals("OCR completed", ocrResult.message)
        assertEquals("Test Receipt", ocrResult.results[0].text)
    }

    @Test
    fun `test OCR request data class`() {
        val request = OCRRequest(
            image = "base64encodedimage",
            det = true,
            rec = true,
            cls = false
        )

        assertEquals("base64encodedimage", request.image)
        assertEquals(true, request.det)
        assertEquals(true, request.rec)
        assertEquals(false, request.cls)
    }

    @Test
    fun `test initialization with API mode`() = runBlocking {
        val config = PaddleOCRConfig(useLocalModel = false)
        val engine = PaddleOCREngine(mockContext, config)
        
        val result = engine.initialize()
        assertTrue(result) // API mode should always return true
    }

    @Test
    fun `test cleanup method`() {
        // Test that cleanup doesn't throw exceptions
        paddleOCREngine.cleanup()
        // If we reach here without exception, the test passes
        assertTrue(true)
    }
}

@RunWith(MockitoJUnitRunner::class)
class ReceiptScannerTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockBitmap: Bitmap

    private lateinit var receiptScanner: ReceiptScanner

    @Before
    fun setup() {
        receiptScanner = ReceiptScanner(mockContext, OCREngine.ML_KIT)
    }

    @Test
    fun `test OCR engine enum`() {
        assertEquals(2, OCREngine.values().size)
        assertTrue(OCREngine.values().contains(OCREngine.ML_KIT))
        assertTrue(OCREngine.values().contains(OCREngine.PADDLE_OCR))
    }

    @Test
    fun `test ReceiptScanner initialization with ML Kit`() = runBlocking {
        val scanner = ReceiptScanner(mockContext, OCREngine.ML_KIT)
        val result = scanner.initialize()
        assertTrue(result) // ML Kit should always return true
    }

    @Test
    fun `test ReceiptScanner cleanup`() {
        receiptScanner.cleanup()
        // If we reach here without exception, the test passes
        assertTrue(true)
    }

    @Test
    fun `test convertOCRResultToText with success`() {
        val textResults = listOf(
            OCRTextResult("Line 1", 0.9f, listOf(0, 0, 100, 20)),
            OCRTextResult("Line 2", 0.8f, listOf(0, 20, 100, 40))
        )
        
        val ocrResult = OCRResult(
            status = "success",
            results = textResults,
            message = "Success"
        )

        // We can't directly test the private method, but we can test the data structure
        val expectedText = textResults.joinToString("\n") { it.text }
        assertEquals("Line 1\nLine 2", expectedText)
    }

    @Test
    fun `test convertOCRResultToText with error`() {
        val ocrResult = OCRResult(
            status = "error",
            results = emptyList(),
            message = "API Error"
        )

        // Test the error case structure
        assertEquals("error", ocrResult.status)
        assertTrue(ocrResult.results.isEmpty())
        assertEquals("API Error", ocrResult.message)
    }
}
